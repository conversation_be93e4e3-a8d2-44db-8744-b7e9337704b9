#!/usr/bin/env python3
"""
Qwen3 GPU环境诊断脚本
"""

import os
import sys
import torch
import subprocess
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

def print_header():
    """打印标题"""
    console.print(Panel.fit(
        "[bold blue]🔍 Qwen3 GPU环境诊断[/bold blue]\n"
        "[dim]诊断GPU环境中的Qwen3模型问题[/dim]",
        border_style="blue"
    ))

def check_environment():
    """检查环境信息"""
    console.print("\n🔧 环境信息检查")
    console.print("=" * 50)
    
    # Python版本
    console.print(f"Python版本: {sys.version}")
    
    # PyTorch信息
    console.print(f"PyTorch版本: {torch.__version__}")
    console.print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        console.print(f"CUDA版本: {torch.version.cuda}")
        console.print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            console.print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 检查关键包版本
    packages = ['transformers', 'sentence-transformers', 'tokenizers', 'safetensors']
    
    table = Table(title="包版本信息")
    table.add_column("包名", style="cyan")
    table.add_column("版本", style="green")
    table.add_column("状态", style="yellow")
    
    for pkg in packages:
        try:
            module = __import__(pkg.replace('-', '_'))
            version = getattr(module, '__version__', 'Unknown')
            
            # 检查版本要求
            status = "✅"
            if pkg == 'transformers' and version:
                from packaging import version as pkg_version
                if pkg_version.parse(version) < pkg_version.parse("4.51.0"):
                    status = "❌ 需要 >= 4.51.0"
            elif pkg == 'sentence-transformers' and version:
                from packaging import version as pkg_version
                if pkg_version.parse(version) < pkg_version.parse("2.7.0"):
                    status = "❌ 需要 >= 2.7.0"
                    
            table.add_row(pkg, version, status)
        except ImportError:
            table.add_row(pkg, "未安装", "❌")
    
    console.print(table)

def check_model_files():
    """检查模型文件"""
    console.print("\n📁 模型文件检查")
    console.print("=" * 50)
    
    # 检查HuggingFace缓存
    hf_home = os.environ.get('HF_HOME', os.path.expanduser('~/.cache/huggingface'))
    console.print(f"HF_HOME: {hf_home}")
    
    models_to_check = [
        "models--Qwen--Qwen3-Embedding-0.6B",
        "models--Qwen--Qwen3-Reranker-0.6B"
    ]
    
    hub_cache = os.path.join(hf_home, 'hub')
    
    for model_dir in models_to_check:
        model_path = os.path.join(hub_cache, model_dir)
        if os.path.exists(model_path):
            console.print(f"✅ {model_dir} 存在")
            
            # 检查关键文件
            snapshots_dir = os.path.join(model_path, 'snapshots')
            if os.path.exists(snapshots_dir):
                snapshots = os.listdir(snapshots_dir)
                if snapshots:
                    latest_snapshot = os.path.join(snapshots_dir, snapshots[0])
                    key_files = ['config.json', 'tokenizer.json', 'model.safetensors']
                    
                    for key_file in key_files:
                        file_path = os.path.join(latest_snapshot, key_file)
                        if os.path.exists(file_path):
                            file_size = os.path.getsize(file_path)
                            console.print(f"  ✅ {key_file}: {file_size:,} bytes")
                        else:
                            console.print(f"  ❌ {key_file}: 缺失")
        else:
            console.print(f"❌ {model_dir} 不存在")

def test_basic_loading():
    """测试基本加载"""
    console.print("\n🧪 基本加载测试")
    console.print("=" * 50)
    
    try:
        from transformers import AutoTokenizer, AutoConfig
        
        # 测试配置加载
        console.print("📝 测试配置加载...")
        config = AutoConfig.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        console.print(f"✅ 配置加载成功: {config.model_type}")
        
        # 测试tokenizer加载
        console.print("📝 测试tokenizer加载...")
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        console.print("✅ Tokenizer加载成功")
        
        # 测试简单编码
        test_text = "测试文本"
        tokens = tokenizer.encode(test_text)
        console.print(f"✅ 编码测试成功: {len(tokens)} tokens")
        
        return True
        
    except Exception as e:
        console.print(f"❌ 基本加载失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    console.print("\n🧪 模型加载测试")
    console.print("=" * 50)
    
    try:
        from transformers import AutoModel
        
        console.print("📝 测试模型加载...")
        model = AutoModel.from_pretrained(
            "Qwen/Qwen3-Embedding-0.6B", 
            trust_remote_code=True,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None
        )
        console.print("✅ 模型加载成功")
        
        return True
        
    except Exception as e:
        console.print(f"❌ 模型加载失败: {e}")
        return False

def test_sentence_transformers():
    """测试SentenceTransformers"""
    console.print("\n🧪 SentenceTransformers测试")
    console.print("=" * 50)
    
    try:
        from sentence_transformers import SentenceTransformer
        
        console.print("📝 测试SentenceTransformer加载...")
        model = SentenceTransformer(
            "Qwen/Qwen3-Embedding-0.6B", 
            trust_remote_code=True,
            device="cuda" if torch.cuda.is_available() else "cpu"
        )
        console.print("✅ SentenceTransformer加载成功")
        
        # 测试编码
        texts = ["测试文本1", "测试文本2"]
        embeddings = model.encode(texts)
        console.print(f"✅ 编码成功: {embeddings.shape}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ SentenceTransformers失败: {e}")
        return False

def clear_cache():
    """清理缓存"""
    console.print("\n🧹 清理缓存")
    console.print("=" * 50)
    
    try:
        # 清理HuggingFace缓存
        hf_home = os.environ.get('HF_HOME', os.path.expanduser('~/.cache/huggingface'))
        
        models_to_clear = [
            "models--Qwen--Qwen3-Embedding-0.6B",
            "models--Qwen--Qwen3-Reranker-0.6B"
        ]
        
        hub_cache = os.path.join(hf_home, 'hub')
        
        for model_dir in models_to_clear:
            model_path = os.path.join(hub_cache, model_dir)
            if os.path.exists(model_path):
                import shutil
                shutil.rmtree(model_path)
                console.print(f"✅ 已清理: {model_dir}")
        
        console.print("✅ 缓存清理完成")
        
    except Exception as e:
        console.print(f"❌ 缓存清理失败: {e}")

def upgrade_packages():
    """升级包"""
    console.print("\n⬆️ 升级包")
    console.print("=" * 50)
    
    packages_to_upgrade = [
        "transformers>=4.52.0",
        "sentence-transformers>=3.0.0",
        "tokenizers>=0.20.0",
        "safetensors>=0.4.0"
    ]
    
    for package in packages_to_upgrade:
        try:
            console.print(f"📦 升级 {package}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", package,
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
            ], check=True, capture_output=True)
            console.print(f"✅ {package} 升级成功")
        except subprocess.CalledProcessError as e:
            console.print(f"❌ {package} 升级失败")

def main():
    """主函数"""
    print_header()
    
    # 检查环境
    check_environment()
    
    # 检查模型文件
    check_model_files()
    
    # 测试基本加载
    basic_ok = test_basic_loading()
    
    if not basic_ok:
        console.print("\n🔧 尝试修复...")
        
        # 清理缓存
        clear_cache()
        
        # 升级包
        upgrade_packages()
        
        console.print("\n⚠️ 请重启Python环境后重新测试")
        return
    
    # 测试模型加载
    model_ok = test_model_loading()
    
    # 测试SentenceTransformers
    st_ok = test_sentence_transformers()
    
    # 总结
    console.print("\n📊 诊断总结")
    console.print("=" * 50)
    
    results = [
        ("基本加载", basic_ok),
        ("模型加载", model_ok),
        ("SentenceTransformers", st_ok)
    ]
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        console.print(f"{name}: {status}")
    
    success_count = sum(result for _, result in results)
    console.print(f"\n总体结果: {success_count}/{len(results)} 通过")
    
    if success_count == len(results):
        console.print("🎉 所有测试通过！")
    else:
        console.print("⚠️ 存在问题，请检查上述错误信息")

if __name__ == "__main__":
    main()
