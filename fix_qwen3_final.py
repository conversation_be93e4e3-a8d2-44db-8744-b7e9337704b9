#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Qwen3 Fix Script / 最终Qwen3修复脚本

This script provides the ultimate fix for Qwen3 model loading issues.
该脚本为Qwen3模型加载问题提供终极修复。

Usage / 使用方法:
  python fix_qwen3_final.py
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path

def setup_environment():
    """Setup environment variables / 设置环境变量"""
    print("🔧 Setting up environment...")
    
    env_vars = {
        "TOKENIZERS_PARALLELISM": "false",
        "HF_HUB_DISABLE_PROGRESS_BARS": "1",
        "TRANSFORMERS_OFFLINE": "0",
        "HF_HUB_OFFLINE": "0",
        "CUDA_VISIBLE_DEVICES": "0",
        "PYTORCH_CUDA_ALLOC_CONF": "max_split_size_mb:512",
        "HF_HOME": str(Path.home() / ".cache" / "huggingface"),
        "TRANSFORMERS_CACHE": str(Path.home() / ".cache" / "huggingface" / "transformers")
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   {key}={value}")
    
    print("✅ Environment configured")

def downgrade_transformers():
    """Downgrade transformers to a stable version / 降级transformers到稳定版本"""
    print("\n🔄 Downgrading transformers to stable version...")
    
    try:
        # Uninstall current version
        subprocess.run([
            sys.executable, "-m", "pip", "uninstall", "transformers", "-y"
        ], check=True, capture_output=True, text=True)
        
        # Install stable version
        subprocess.run([
            sys.executable, "-m", "pip", "install", "transformers==4.44.2"
        ], check=True, capture_output=True, text=True)
        
        print("✅ Transformers downgraded to 4.44.2")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to downgrade transformers: {e}")
        return False

def create_sentence_transformer_configs():
    """Create all necessary SentenceTransformer configs / 创建所有必要的SentenceTransformer配置"""
    print("\n🔧 Creating SentenceTransformer configs...")
    
    cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
    
    # Find all Qwen3 model directories
    qwen3_patterns = [
        "models--Qwen--Qwen3-Embedding-*",
        "models--Qwen--Qwen3-Reranker-*"
    ]
    
    for pattern in qwen3_patterns:
        model_dirs = list(cache_dir.glob(pattern))
        
        for model_dir in model_dirs:
            print(f"   Processing: {model_dir.name}")
            
            # Find snapshots
            snapshots_dir = model_dir / "snapshots"
            if not snapshots_dir.exists():
                continue
            
            for snapshot_dir in snapshots_dir.iterdir():
                if snapshot_dir.is_dir():
                    # Create sentence transformer config
                    config_file = snapshot_dir / "sentence_xlnet_config.json"
                    
                    config = {
                        "do_lower_case": False,
                        "remove_duplicates": False,
                        "tokenizer_class": "AutoTokenizer",
                        "tokenizer_name_or_path": str(snapshot_dir)
                    }
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2)
                    
                    print(f"   ✅ Created: {config_file}")
                    
                    # Also create modules.json for SentenceTransformers
                    modules_file = snapshot_dir / "modules.json"
                    modules_config = [
                        {
                            "idx": 0,
                            "name": "0",
                            "path": "",
                            "type": "sentence_transformers.models.Transformer"
                        },
                        {
                            "idx": 1,
                            "name": "1",
                            "path": "1_Pooling",
                            "type": "sentence_transformers.models.Pooling"
                        }
                    ]
                    
                    with open(modules_file, 'w', encoding='utf-8') as f:
                        json.dump(modules_config, f, indent=2)
                    
                    print(f"   ✅ Created: {modules_file}")
                    
                    # Create pooling config
                    pooling_dir = snapshot_dir / "1_Pooling"
                    pooling_dir.mkdir(exist_ok=True)
                    
                    pooling_config_file = pooling_dir / "config.json"
                    pooling_config = {
                        "word_embedding_dimension": 1024,
                        "pooling_mode_cls_token": False,
                        "pooling_mode_mean_tokens": False,
                        "pooling_mode_max_tokens": False,
                        "pooling_mode_mean_sqrt_len_tokens": False,
                        "pooling_mode_weightedmean_tokens": False,
                        "pooling_mode_lasttoken": True
                    }
                    
                    with open(pooling_config_file, 'w', encoding='utf-8') as f:
                        json.dump(pooling_config, f, indent=2)
                    
                    print(f"   ✅ Created: {pooling_config_file}")
    
    print("✅ All SentenceTransformer configs created")

def test_basic_transformers():
    """Test basic transformers functionality / 测试基本transformers功能"""
    print("\n🧪 Testing basic transformers...")
    
    try:
        from transformers import AutoTokenizer
        
        # Test with a simple model first
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
        print("✅ Basic transformers working")
        return True
        
    except Exception as e:
        print(f"❌ Basic transformers failed: {e}")
        return False

def test_qwen3_direct():
    """Test Qwen3 with direct transformers approach / 使用直接transformers方法测试Qwen3"""
    print("\n🧪 Testing Qwen3 with direct approach...")
    
    try:
        from transformers import AutoTokenizer, AutoModel
        import torch
        
        model_name = "Qwen/Qwen3-Embedding-0.6B"
        
        # Load with minimal configuration
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            use_fast=False,
            local_files_only=False
        )
        print("✅ Tokenizer loaded")
        
        # Load model with CPU first to avoid GPU issues
        model = AutoModel.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype=torch.float32,
            device_map="cpu",
            low_cpu_mem_usage=True
        )
        print("✅ Model loaded on CPU")
        
        # Test encoding
        texts = ["Hello", "World"]
        inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model(**inputs)
            embeddings = outputs.last_hidden_state.mean(dim=1)
        
        print(f"✅ Encoding successful - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Direct approach failed: {e}")
        return False

def test_sentence_transformers():
    """Test SentenceTransformers after fixes / 修复后测试SentenceTransformers"""
    print("\n🧪 Testing SentenceTransformers after fixes...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Try with minimal configuration
        model = SentenceTransformer(
            "Qwen/Qwen3-Embedding-0.6B",
            trust_remote_code=True
        )
        
        texts = ["Hello", "World"]
        embeddings = model.encode(texts, show_progress_bar=False)
        
        print(f"✅ SentenceTransformers working - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ SentenceTransformers failed: {e}")
        return False

def create_working_test():
    """Create a working test script / 创建可工作的测试脚本"""
    print("\n🔧 Creating working test script...")
    
    test_code = '''#!/usr/bin/env python3
import os
import torch
from transformers import AutoTokenizer, AutoModel
import torch.nn.functional as F

# Set environment
os.environ["TOKENIZERS_PARALLELISM"] = "false"

def test_qwen3():
    """Test Qwen3 with working configuration"""
    try:
        model_name = "Qwen/Qwen3-Embedding-0.6B"
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            use_fast=False
        )
        
        # Load model
        model = AutoModel.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype=torch.float32,
            device_map="cpu"
        )
        
        # Test encoding
        texts = ["Hello world", "Test sentence"]
        inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model(**inputs)
            embeddings = outputs.last_hidden_state.mean(dim=1)
            embeddings = F.normalize(embeddings, p=2, dim=1)
        
        print(f"✅ Qwen3 working - shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_qwen3()
'''
    
    try:
        with open("test_qwen3_working.py", "w", encoding="utf-8") as f:
            f.write(test_code)
        
        print("✅ Working test script created: test_qwen3_working.py")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test script: {e}")
        return False

def main():
    """Main fix process / 主修复流程"""
    print("🚀 Final Qwen3 Fix Script")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Downgrade transformers
    if not downgrade_transformers():
        print("⚠️  Transformers downgrade failed, continuing...")
    
    # Create configs
    create_sentence_transformer_configs()
    
    # Run tests
    tests = [
        ("Basic Transformers", test_basic_transformers),
        ("Qwen3 Direct", test_qwen3_direct),
        ("SentenceTransformers", test_sentence_transformers)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Create working test
    create_working_test()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed >= 1:
        print("\n🎉 At least one method working!")
        print("\n📋 Next steps:")
        print("1. Run: python test_qwen3_working.py")
        print("2. If working, run: python scripts/01_setup_and_test.py")
        return 0
    else:
        print("\n❌ All tests failed. Manual intervention required.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
