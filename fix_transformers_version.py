#!/usr/bin/env python3
"""
Qwen3 Transformers Version Fix Script
修复 transformers 版本以支持 Qwen3 模型

根据官方文档，Qwen3 模型需要 transformers >= 4.51.0
"""

import os
import sys
import subprocess
import importlib.util
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

console = Console()

def print_header():
    """打印脚本标题"""
    console.print(Panel.fit(
        "[bold blue]🔧 Qwen3 Transformers Version Fix Script[/bold blue]\n"
        "[dim]修复 transformers 版本以支持 Qwen3 模型[/dim]",
        border_style="blue"
    ))

def check_current_version():
    """检查当前 transformers 版本"""
    try:
        import transformers
        current_version = transformers.__version__
        console.print(f"📦 当前 transformers 版本: [yellow]{current_version}[/yellow]")
        
        # 检查版本是否满足要求
        from packaging import version
        required_version = "4.51.0"
        
        if version.parse(current_version) >= version.parse(required_version):
            console.print(f"✅ 版本满足要求 (>= {required_version})")
            return True, current_version
        else:
            console.print(f"❌ 版本不满足要求 (需要 >= {required_version})")
            return False, current_version
            
    except ImportError:
        console.print("❌ transformers 未安装")
        return False, None

def upgrade_transformers():
    """升级 transformers 到最新版本"""
    console.print("\n🔄 升级 transformers...")
    
    try:
        # 使用清华镜像源加速下载
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "--upgrade", "transformers>=4.51.0",
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("正在升级 transformers...", total=None)
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                check=True
            )
            
            progress.update(task, completed=True)
        
        console.print("✅ transformers 升级成功")
        return True
        
    except subprocess.CalledProcessError as e:
        console.print(f"❌ 升级失败: {e}")
        console.print(f"错误输出: {e.stderr}")
        return False

def upgrade_sentence_transformers():
    """升级 sentence-transformers"""
    console.print("\n🔄 升级 sentence-transformers...")
    
    try:
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "--upgrade", "sentence-transformers>=2.7.0",
            "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("正在升级 sentence-transformers...", total=None)
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                check=True
            )
            
            progress.update(task, completed=True)
        
        console.print("✅ sentence-transformers 升级成功")
        return True
        
    except subprocess.CalledProcessError as e:
        console.print(f"❌ 升级失败: {e}")
        console.print(f"错误输出: {e.stderr}")
        return False

def test_qwen3_models():
    """测试 Qwen3 模型加载"""
    console.print("\n🧪 测试 Qwen3 模型...")
    
    # 重新导入以获取新版本
    if 'transformers' in sys.modules:
        importlib.reload(sys.modules['transformers'])
    
    try:
        from transformers import AutoTokenizer, AutoModel
        
        # 测试 Embedding 模型
        console.print("📝 测试 Qwen3-Embedding-0.6B...")
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        model = AutoModel.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        console.print("✅ Qwen3-Embedding-0.6B 加载成功")
        
        # 测试 Reranker 模型
        console.print("📝 测试 Qwen3-Reranker-0.6B...")
        tokenizer_rerank = AutoTokenizer.from_pretrained("Qwen/Qwen3-Reranker-0.6B", trust_remote_code=True)
        model_rerank = AutoModel.from_pretrained("Qwen/Qwen3-Reranker-0.6B", trust_remote_code=True)
        console.print("✅ Qwen3-Reranker-0.6B 加载成功")
        
        return True
        
    except Exception as e:
        console.print(f"❌ 模型加载失败: {e}")
        return False

def test_sentence_transformers():
    """测试 SentenceTransformers"""
    console.print("\n🧪 测试 SentenceTransformers...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # 测试 Embedding 模型
        console.print("📝 测试 SentenceTransformer Embedding...")
        model = SentenceTransformer("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        
        # 简单测试
        sentences = ["这是一个测试句子", "This is a test sentence"]
        embeddings = model.encode(sentences)
        console.print(f"✅ 嵌入维度: {embeddings.shape}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ SentenceTransformers 测试失败: {e}")
        return False

def create_test_script():
    """创建测试脚本"""
    test_script = '''#!/usr/bin/env python3
"""
Qwen3 模型测试脚本
"""

import torch
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer
from rich.console import Console

console = Console()

def test_transformers_approach():
    """测试 transformers 方式"""
    console.print("🧪 测试 Transformers 方式...")
    
    try:
        # 加载模型
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        model = AutoModel.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        
        # 测试文本
        texts = ["医生，我最近总是头痛，该怎么办？", "Doctor, I have been having headaches lately, what should I do?"]
        
        # 编码
        inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt", max_length=512)
        
        with torch.no_grad():
            outputs = model(**inputs)
            embeddings = outputs.last_hidden_state.mean(dim=1)
        
        console.print(f"✅ Transformers 方式成功，嵌入维度: {embeddings.shape}")
        return True
        
    except Exception as e:
        console.print(f"❌ Transformers 方式失败: {e}")
        return False

def test_sentence_transformers_approach():
    """测试 SentenceTransformers 方式"""
    console.print("🧪 测试 SentenceTransformers 方式...")
    
    try:
        # 加载模型
        model = SentenceTransformer("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        
        # 测试文本
        texts = ["医生，我最近总是头痛，该怎么办？", "Doctor, I have been having headaches lately, what should I do?"]
        
        # 编码
        embeddings = model.encode(texts)
        
        console.print(f"✅ SentenceTransformers 方式成功，嵌入维度: {embeddings.shape}")
        return True
        
    except Exception as e:
        console.print(f"❌ SentenceTransformers 方式失败: {e}")
        return False

if __name__ == "__main__":
    console.print("🚀 Qwen3 模型测试")
    console.print("=" * 50)
    
    success_count = 0
    
    if test_transformers_approach():
        success_count += 1
    
    if test_sentence_transformers_approach():
        success_count += 1
    
    console.print("=" * 50)
    console.print(f"📊 测试结果: {success_count}/2 通过")
    
    if success_count == 2:
        console.print("🎉 所有测试通过！Qwen3 模型可以正常使用")
    else:
        console.print("⚠️ 部分测试失败，请检查配置")
'''
    
    with open("test_qwen3_final.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    console.print("✅ 测试脚本已创建: test_qwen3_final.py")

def main():
    """主函数"""
    print_header()
    
    # 检查当前版本
    is_compatible, current_version = check_current_version()
    
    if is_compatible:
        console.print("\n🎉 当前版本已满足要求，直接测试模型...")
    else:
        # 升级 transformers
        if not upgrade_transformers():
            console.print("❌ 升级失败，退出")
            return False
        
        # 升级 sentence-transformers
        if not upgrade_sentence_transformers():
            console.print("⚠️ sentence-transformers 升级失败，但可能不影响基本功能")
    
    # 重启 Python 解释器提示
    console.print("\n⚠️ 需要重启 Python 环境以加载新版本")
    console.print("请运行以下命令重新测试:")
    console.print("[bold green]python test_qwen3_final.py[/bold green]")
    
    # 创建测试脚本
    create_test_script()
    
    # 显示版本信息
    table = Table(title="版本信息")
    table.add_column("包名", style="cyan")
    table.add_column("要求版本", style="magenta")
    table.add_column("当前版本", style="green")
    
    try:
        import transformers
        table.add_row("transformers", ">=4.51.0", transformers.__version__)
    except:
        table.add_row("transformers", ">=4.51.0", "未安装")
    
    try:
        import sentence_transformers
        table.add_row("sentence-transformers", ">=2.7.0", sentence_transformers.__version__)
    except:
        table.add_row("sentence-transformers", ">=2.7.0", "未安装")
    
    console.print(table)
    
    return True

if __name__ == "__main__":
    main()
