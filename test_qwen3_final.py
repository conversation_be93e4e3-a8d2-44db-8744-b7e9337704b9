#!/usr/bin/env python3
"""
Qwen3 模型测试脚本
"""

import torch
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer
from rich.console import Console

console = Console()

def test_transformers_approach():
    """测试 transformers 方式"""
    console.print("🧪 测试 Transformers 方式...")
    
    try:
        # 加载模型
        tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        model = AutoModel.from_pretrained("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        
        # 测试文本
        texts = ["医生，我最近总是头痛，该怎么办？", "Doctor, I have been having headaches lately, what should I do?"]
        
        # 编码
        inputs = tokenizer(texts, padding=True, truncation=True, return_tensors="pt", max_length=512)
        
        with torch.no_grad():
            outputs = model(**inputs)
            embeddings = outputs.last_hidden_state.mean(dim=1)
        
        console.print(f"✅ Transformers 方式成功，嵌入维度: {embeddings.shape}")
        return True
        
    except Exception as e:
        console.print(f"❌ Transformers 方式失败: {e}")
        return False

def test_sentence_transformers_approach():
    """测试 SentenceTransformers 方式"""
    console.print("🧪 测试 SentenceTransformers 方式...")
    
    try:
        # 加载模型
        model = SentenceTransformer("Qwen/Qwen3-Embedding-0.6B", trust_remote_code=True)
        
        # 测试文本
        texts = ["医生，我最近总是头痛，该怎么办？", "Doctor, I have been having headaches lately, what should I do?"]
        
        # 编码
        embeddings = model.encode(texts)
        
        console.print(f"✅ SentenceTransformers 方式成功，嵌入维度: {embeddings.shape}")
        return True
        
    except Exception as e:
        console.print(f"❌ SentenceTransformers 方式失败: {e}")
        return False

if __name__ == "__main__":
    console.print("🚀 Qwen3 模型测试")
    console.print("=" * 50)
    
    success_count = 0
    
    if test_transformers_approach():
        success_count += 1
    
    if test_sentence_transformers_approach():
        success_count += 1
    
    console.print("=" * 50)
    console.print(f"📊 测试结果: {success_count}/2 通过")
    
    if success_count == 2:
        console.print("🎉 所有测试通过！Qwen3 模型可以正常使用")
    else:
        console.print("⚠️ 部分测试失败，请检查配置")
